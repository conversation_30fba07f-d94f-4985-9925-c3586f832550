import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UserWrapper } from "../../../components/UserWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { useSDK } from "../../../hooks/useSDK";

interface IListingForm {
  name: string;
  description: string;
  price: string;
  category: string;
  image: string;
  status: string;
}

const UserAddListingPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<IListingForm>({
    name: "",
    description: "",
    price: "",
    category: "",
    image: "",
    status: "active",
  });

  const categories = [
    { value: "", label: "Select a category" },
    { value: "electronics", label: "Electronics" },
    { value: "clothing", label: "Clothing" },
    { value: "home", label: "Home & Garden" },
    { value: "sports", label: "Sports" },
    { value: "books", label: "Books" },
    { value: "other", label: "Other" },
  ];

  const handleInputChange = (field: keyof IListingForm, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.name.trim()) {
      alert("Please enter a listing name");
      return;
    }
    if (!formData.description.trim()) {
      alert("Please enter a description");
      return;
    }
    if (!formData.price.trim() || isNaN(parseFloat(formData.price))) {
      alert("Please enter a valid price");
      return;
    }
    if (!formData.category) {
      alert("Please select a category");
      return;
    }

    setLoading(true);
    try {
      const result = await sdk.callRestAPI(
        {
          name: formData.name.trim(),
          description: formData.description.trim(),
          price: parseFloat(formData.price),
          category: formData.category,
          image: formData.image.trim() || null,
          status: formData.status,
          // user_id would be automatically set by the backend from auth context
        },
        "POST"
      );

      if (result.error) {
        console.error("Error creating listing:", result.message);
        alert("Failed to create listing. Please try again.");
      } else {
        alert("Listing created successfully!");
        navigate("/user/listings");
      }
    } catch (error) {
      console.error("Error creating listing:", error);
      alert("Failed to create listing. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <UserWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <InteractiveButton
            onClick={() => navigate("/user/listings")}
            className="text-white hover:text-gray-300 mb-4"
          >
            ← Back to My Listings..
          </InteractiveButton>
          <h1 className="text-3xl font-bold text-white mb-2">
            Add New Listing
          </h1>
          <p className="text-gray-300">
            Create a new listing to sell your item
          </p>
          <p className="text-gray-300">
            Create a new listing to sell your item
          </p>
          <p className="text-gray-300">
            Create a new listing to sell your item
          </p>
          <p className="text-gray-300">
            Create a new listing to sell your item
          </p>
        </div>

        <div className="bg-white rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Listing Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Listing Name *
              </label>
              <MkdInputV2
                placeholder="Enter listing name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
              >
                <MkdInputV2.Field />
              </MkdInputV2>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                rows={4}
                placeholder="Describe your item in detail"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                required
              />
            </div>

            {/* Price and Category Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <MkdInputV2
                    type="number"
                    placeholder="0.00"
                    className="pl-8"
                    value={formData.price}
                    onChange={(e) => handleInputChange("price", e.target.value)}
                    required
                  >
                    <MkdInputV2.Field step="0.01" min="0" />
                  </MkdInputV2>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange("category", e.target.value)
                  }
                  required
                >
                  {categories.map((cat) => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Image URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image URL (optional)
              </label>
              <MkdInputV2
                type="url"
                placeholder="https://example.com/image.jpg"
                value={formData.image}
                onChange={(e) => handleInputChange("image", e.target.value)}
              >
                <MkdInputV2.Field />
              </MkdInputV2>
              <p className="text-sm text-gray-500 mt-1">
                Provide a URL to an image of your item
              </p>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
              >
                <option value="active">Active</option>
                <option value="draft">Draft</option>
              </select>
              <p className="text-sm text-gray-500 mt-1">
                Active listings will be visible in the marketplace
              </p>
            </div>

            {/* Image Preview */}
            {formData.image && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image Preview
                </label>
                <div className="border border-gray-300 rounded-md p-4">
                  <img
                    src={formData.image}
                    alt="Preview"
                    className="max-w-xs max-h-48 object-cover rounded-md"
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex gap-4 pt-6">
              <InteractiveButton
                type="submit"
                disabled={loading}
                className="flex-1 bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {loading ? "Creating..." : "Create Listing"}
              </InteractiveButton>

              <InteractiveButton
                type="button"
                onClick={() => navigate("/user/listings")}
                className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50"
              >
                Cancel
              </InteractiveButton>
            </div>
          </form>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserAddListingPage;
