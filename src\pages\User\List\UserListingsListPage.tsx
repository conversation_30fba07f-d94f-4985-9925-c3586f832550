import React, { useEffect, useState, useCallback } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { PaginationBar } from "@/components/PaginationBar";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { PlusIcon, EditIcon, TrashIcon } from "@/assets/svgs";
import ProvideShippingInfoModal from "@/components/ProvideShippingInfoModal";

interface IListing {
  id: number;
  name: string;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  type?: string;
  created_at: string;
  updated_at: string;
  buyer?: string;
  sold_date?: string;
  delivery_status?: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const UserListingsListPage = () => {
  const navigate = useNavigate();
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [activeTab, setActiveTab] = useState<"all" | "sold">("all");
  const [showFilters, setShowFilters] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 6,
    search: "",
    status: "All Statuses",
    type: "All",
    category: "All Categories",
    dateAdded: "Any Time",
    sortBy: "Newest Sold",
    priceRange: { min: "", max: "" },
  });
  const [searchInput, setSearchInput] = useState("");
  const [isShippingModalOpen, setIsShippingModalOpen] = useState(false);
  const [selectedListingId, setSelectedListingId] = useState<number | null>(
    null
  );

  const { sdk } = useSDK();

  // Mock data for all listings - replace with actual API call
  const mockAllListings: IListing[] = [
    {
      id: 1,
      name: "Wireless Headphones",
      price: "129.99",
      status: "active",
      description: "Noise-cancelling wireless headphones with 30h battery life",
      image: "/api/placeholder/300/200",
      category: "Electronics",
      type: "Item",
      created_at: "2024-04-10",
      updated_at: "2024-04-10",
      buyer: "",
      sold_date: "Added Apr 10, '25",
      delivery_status: "Active",
    },
    {
      id: 2,
      name: "Mountain Bike",
      price: "420.00",
      status: "expired",
      description: "21-speed mountain bike, great condition, recently serviced",
      image: "/api/placeholder/300/200",
      category: "Sports",
      type: "Item",
      created_at: "2024-02-20",
      updated_at: "2024-02-20",
      buyer: "",
      sold_date: "Added Feb 20, '25",
      delivery_status: "Expired 2 days ago",
    },
    {
      id: 3,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 4,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-18",
      updated_at: "2024-04-18",
      buyer: "",
      sold_date: "Saved Apr 18, '25",
      delivery_status: "Not published yet",
    },
    {
      id: 5,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 6,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-15",
      updated_at: "2024-04-15",
      buyer: "",
      sold_date: "Not published yet",
      delivery_status: "Saved Apr 15, '25",
    },
    {
      id: 7,
      name: "Mountain Bike",
      price: "420.00",
      status: "expired",
      description: "21-speed mountain bike, great condition, recently serviced",
      image: "/api/placeholder/300/200",
      category: "Sports",
      type: "Item",
      created_at: "2024-02-20",
      updated_at: "2024-02-20",
      buyer: "",
      sold_date: "Added Feb 20, '25",
      delivery_status: "Expired 2 days ago",
    },
    {
      id: 8,
      name: "Graphic Design Services",
      price: "75.00",
      status: "sponsored",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-05",
      updated_at: "2024-04-05",
      buyer: "",
      sold_date: "Added Apr 5, '25",
      delivery_status: "Sponsored",
    },
    {
      id: 9,
      name: "Antique Wooden Desk",
      price: "350.00",
      status: "draft",
      description: "Early 1900s oak writing desk with original hardware",
      image: "/api/placeholder/300/200",
      category: "Furniture",
      type: "Item",
      created_at: "2024-04-15",
      updated_at: "2024-04-15",
      buyer: "",
      sold_date: "Not published yet",
      delivery_status: "Saved Apr 15, '25",
    },
  ];

  // Mock data for sold and action needed items
  const mockSoldListings: IListing[] = [
    {
      id: 101,
      name: "Graphic Design Services",
      price: "75.00",
      status: "booked",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-25",
      updated_at: "2024-04-25",
      buyer: "Emma Davis",
      sold_date: "Booked: April 25, 3:00-5:00 PM",
      delivery_status: "Booked",
    },
    {
      id: 102,
      name: "Graphic Design Services",
      price: "75.00",
      status: "waiting_confirmation",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-20",
      updated_at: "2024-04-20",
      buyer: "Robert Chen",
      sold_date: "Booked: April 20, 1:00-3:00 PM",
      delivery_status: "Waiting Confirmation",
    },
    {
      id: 103,
      name: "Graphic Design Services",
      price: "75.00",
      status: "completed",
      description:
        "Professional graphic design services for logos, branding, etc.",
      image: "/api/placeholder/300/200",
      category: "Services",
      type: "Service",
      created_at: "2024-04-15",
      updated_at: "2024-04-15",
      buyer: "Jessica Miller",
      sold_date: "Booked: April 15, 2:00-4:00 PM",
      delivery_status: "Completed",
    },
  ];

  const fetchListings = async () => {
    setLoading(true);
    try {
      // For now, use mock data - replace with actual API call later
      setTimeout(() => {
        let filteredListings: IListing[];

        // Filter by tab
        if (activeTab === "sold") {
          filteredListings = mockSoldListings;
        } else {
          // For "all" tab, show all listings
          filteredListings = mockAllListings;
        }

        // Apply search filter
        if (filters.search) {
          filteredListings = filteredListings.filter(
            (listing) =>
              listing.name
                .toLowerCase()
                .includes(filters.search.toLowerCase()) ||
              listing.description
                ?.toLowerCase()
                .includes(filters.search.toLowerCase())
          );
        }

        // Apply status filter
        if (filters.status !== "All Statuses") {
          filteredListings = filteredListings.filter((listing) => {
            if (activeTab === "sold") {
              if (filters.status === "Booked")
                return listing.status === "booked";
              if (filters.status === "Waiting Confirmation")
                return listing.status === "waiting_confirmation";
              if (filters.status === "Completed")
                return listing.status === "completed";
            } else {
              if (filters.status === "Active")
                return listing.status === "active";
              if (filters.status === "Draft") return listing.status === "draft";
              if (filters.status === "Expired")
                return listing.status === "expired";
              if (filters.status === "Sponsored")
                return listing.status === "sponsored";
            }
            return true;
          });
        }

        // Apply type filter
        if (filters.type !== "All") {
          filteredListings = filteredListings.filter(
            (listing) => listing.type === filters.type
          );
        }

        // Apply category filter
        if (filters.category !== "All Categories") {
          filteredListings = filteredListings.filter(
            (listing) => listing.category === filters.category
          );
        }

        setListings(filteredListings);
        setPagination({
          page: filters.page,
          limit: filters.limit,
          total: filteredListings.length,
          num_pages: Math.ceil(filteredListings.length / filters.limit),
          has_next:
            filters.page < Math.ceil(filteredListings.length / filters.limit),
          has_prev: filters.page > 1,
        });
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error("Error fetching listings:", error);
      setListings([]);
      setPagination(null);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListings();
  }, [filters.page, filters.search, activeTab]);

  const handleSearch = useCallback(() => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
      page: 1,
    }));
  }, [searchInput]);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleProvideShippingInfo = (listingId: number) => {
    setSelectedListingId(listingId);
    setIsShippingModalOpen(true);
  };

  const handleShippingInfoSubmit = (shippingInfo: {
    carrierName: string;
    trackingNumber: string;
    shippingDate: string;
    proofFile?: File;
  }) => {
    // Here you would typically send the shipping info to your backend
    console.log(
      "Shipping info submitted:",
      shippingInfo,
      "for listing:",
      selectedListingId
    );
    // You can add API call here to update the listing with shipping info
    setIsShippingModalOpen(false);
    setSelectedListingId(null);
  };

  const handleTabChange = (tab: "all" | "sold") => {
    setActiveTab(tab);
    setFilters((prev) => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (filterType: string, value: string | object) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
      page: 1,
    }));
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 text-white";
      case "expired":
        return "bg-red-500 text-white";
      case "sponsored":
        return "bg-yellow-500 text-black";
      case "draft":
        return "bg-gray-500 text-white";
      case "pending_shipping":
        return "bg-orange-500 text-white";
      case "in_transit":
        return "bg-blue-500 text-white";
      case "shipped":
        return "bg-green-500 text-white";
      case "booked":
        return "bg-purple-500 text-white";
      case "waiting_confirmation":
        return "bg-yellow-500 text-white";
      case "completed":
        return "bg-green-600 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "expired":
        return "Expired 2 days ago";
      case "sponsored":
        return "Sponsored";
      case "draft":
        return "Draft";
      case "pending_shipping":
        return "Pending Shipping";
      case "in_transit":
        return "In Transit";
      case "shipped":
        return "Shipped";
      case "booked":
        return "Booked";
      case "waiting_confirmation":
        return "Waiting Confirmation";
      case "completed":
        return "Completed";
      default:
        return status;
    }
  };

  return (
    <UserWrapper>
      <div className="p-6 min-h-screen">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-white">My Listings</h1>
          <Link
            to="/user/listings/add"
            className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f] flex items-center gap-2 text-sm font-medium"
          >
            + Add New Listing
          </Link>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-0">
            <button
              onClick={() => handleTabChange("all")}
              className={`px-6 py-3 text-sm font-medium rounded-l-md border ${
                activeTab === "all"
                  ? "bg-[#E63946] text-white border-[#E63946]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              All Listings
            </button>
            <button
              onClick={() => handleTabChange("sold")}
              className={`px-6 py-3 text-sm font-medium rounded-r-md border-t border-r border-b ${
                activeTab === "sold"
                  ? "bg-[#0F2C59] text-white border-[#0F2C59]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Sold & Action Needed
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6">
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by item name, category, or tag"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.121A1 1 0 013 6.414V4z"
                />
              </svg>
              Filters
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"
                />
              </svg>
              Sort
            </button>
          </div>

          {/* Filter Dropdowns */}
          {showFilters && (
            <>
              <div className="grid grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) =>
                      handleFilterChange("status", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All Statuses</option>
                    {activeTab === "sold" ? (
                      <>
                        <option>Booked</option>
                        <option>Waiting Confirmation</option>
                        <option>Completed</option>
                      </>
                    ) : (
                      <>
                        <option>Active</option>
                        <option>Draft</option>
                        <option>Expired</option>
                        <option>Sponsored</option>
                      </>
                    )}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => handleFilterChange("type", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All</option>
                    <option>Item</option>
                    <option>Service</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) =>
                      handleFilterChange("category", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All Categories</option>
                    <option>Electronics</option>
                    <option>Sports</option>
                    <option>Services</option>
                    <option>Furniture</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {activeTab === "sold" ? "Sort By" : "Date Added"}
                  </label>
                  <select
                    value={
                      activeTab === "sold"
                        ? filters.sortBy || "Newest Sold"
                        : filters.dateAdded
                    }
                    onChange={(e) =>
                      handleFilterChange(
                        activeTab === "sold" ? "sortBy" : "dateAdded",
                        e.target.value
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    {activeTab === "sold" ? (
                      <>
                        <option>Newest Sold</option>
                        <option>Oldest Sold</option>
                        <option>Price High to Low</option>
                        <option>Price Low to High</option>
                      </>
                    ) : (
                      <>
                        <option>Any Time</option>
                        <option>Last 7 days</option>
                        <option>Last 30 days</option>
                        <option>Last 3 months</option>
                      </>
                    )}
                  </select>
                </div>
              </div>

              {/* Price Range Row */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price Range (eBa$)
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Min"
                      value={filters.priceRange.min}
                      onChange={(e) =>
                        handleFilterChange("priceRange", {
                          ...filters.priceRange,
                          min: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                    />
                    <input
                      type="text"
                      placeholder="Max"
                      value={filters.priceRange.max}
                      onChange={(e) =>
                        handleFilterChange("priceRange", {
                          ...filters.priceRange,
                          max: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Listings Grid */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <MkdLoader />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              {listings.map((listing) => (
                <div
                  key={listing.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                >
                  {/* Status Badge */}
                  <div className="relative">
                    <img
                      src={listing.image || "/api/placeholder/300/200"}
                      alt={listing.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <span className="text-xs font-medium text-gray-600 bg-white px-2 py-1 rounded">
                        {listing.type}
                      </span>
                    </div>
                    <div className="absolute top-3 right-3">
                      <span
                        className={`text-xs font-medium px-2 py-1 rounded ${getStatusBadgeColor(listing.status)}`}
                      >
                        {getStatusText(listing.status)}
                      </span>
                    </div>
                    {listing.status === "sponsored" && (
                      <div className="absolute top-3 right-3">
                        <span className="text-xs font-medium px-2 py-1 rounded bg-yellow-400 text-black">
                          ⭐ Sponsored
                        </span>
                      </div>
                    )}
                    {/* Blue offers badge */}
                    {listing.id === 1 && (
                      <div className="absolute top-3 left-3 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                        👥 2 Offers
                      </div>
                    )}
                  </div>

                  {/* Card Content */}
                  <div className="p-4">
                    <h3 className="font-semibold text-[#0F2C59] mb-1 text-base">
                      {listing.name}
                    </h3>
                    <p className="text-[#E63946] font-bold text-lg mb-2">
                      eBa$ {listing.price}
                      {listing.type === "Service" ? "/hr" : ""}
                    </p>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {listing.description}
                    </p>

                    <div className="text-xs text-gray-500 mb-3">
                      <div className="mb-1">{listing.category}</div>
                      {activeTab === "sold" ? (
                        <>
                          <div className="flex items-center gap-1 mb-1">
                            <span>📅 {listing.sold_date}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span>👤 Client: {listing.buyer}</span>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex items-center gap-4 mb-1">
                            <span>Quantity: 1 available | 0 sold</span>
                          </div>
                          <div className="flex items-center gap-4">
                            <span>
                              👁{" "}
                              {listing.id === 1
                                ? "15"
                                : listing.id === 2
                                  ? "12"
                                  : listing.id === 3
                                    ? "35"
                                    : "15"}{" "}
                              views
                            </span>
                            <span>
                              ❓{" "}
                              {listing.id === 1
                                ? "3"
                                : listing.id === 2
                                  ? "2"
                                  : listing.id === 3
                                    ? "8"
                                    : "3"}{" "}
                              inquiries
                            </span>
                            <span>• {listing.sold_date}</span>
                          </div>
                        </>
                      )}
                    </div>

                    {/* Special Messages for Sold Items */}
                    {activeTab === "sold" && listing.status === "completed" && (
                      <div className="text-xs text-green-600 bg-green-50 p-2 rounded mb-3">
                        ⭐ Client left a 5-star review
                      </div>
                    )}

                    {/* Action Buttons */}
                    {activeTab === "sold" ? (
                      <div className="flex gap-2 mb-3">
                        <button className="flex items-center gap-1 text-xs text-[#0F2C59] hover:text-blue-800 border border-[#0F2C59] px-2 py-1 rounded">
                          👁️ View Details
                        </button>
                        <button className="flex items-center gap-1 text-xs text-[#0F2C59] hover:text-blue-800 border border-[#0F2C59] px-2 py-1 rounded">
                          💬 Message
                        </button>
                      </div>
                    ) : (
                      <div className="flex gap-4 mb-3">
                        <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                          ✏️ Edit
                        </button>
                        <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                          🗑️ Delete
                        </button>
                        <button className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800">
                          👁️ View
                        </button>
                      </div>
                    )}

                    {/* Special Action Buttons based on status */}
                    {activeTab === "sold" ? (
                      <>
                        {listing.status === "completed" && (
                          <button className="w-full bg-green-600 text-white text-xs py-2 px-3 rounded hover:bg-green-700 flex items-center justify-center gap-1">
                            ✓ Mark as Completed
                          </button>
                        )}
                        {listing.status === "waiting_confirmation" && (
                          <div className="flex items-center justify-center text-xs text-yellow-600 py-2">
                            ⏳ Waiting for buyer confirmation...
                          </div>
                        )}
                        {listing.status === "booked" && (
                          <>
                            <button
                              onClick={() =>
                                handleProvideShippingInfo(listing.id)
                              }
                              className="w-full bg-[#0F2C59] text-white text-xs py-2 px-3 rounded hover:bg-blue-800 flex items-center justify-center gap-1 mb-2"
                            >
                              📦 Provide Shipping Info
                            </button>
                            <button className="w-full bg-blue-600 text-white text-xs py-2 px-3 rounded hover:bg-blue-700 flex items-center justify-center gap-1">
                              📅 View Details
                            </button>
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        {listing.status === "active" && listing.id === 1 && (
                          <button className="w-full bg-[#E63946] text-white text-xs py-2 px-3 rounded hover:bg-[#d32f2f] flex items-center justify-center gap-1">
                            🚀 Promote
                          </button>
                        )}

                        {listing.status === "draft" && (
                          <button className="w-full bg-[#E63946] text-white text-xs py-2 px-3 rounded hover:bg-[#d32f2f] flex items-center justify-center gap-1">
                            📤 Publish
                          </button>
                        )}

                        {listing.status === "expired" && (
                          <button className="w-full bg-gray-500 text-white text-xs py-2 px-3 rounded hover:bg-gray-600 flex items-center justify-center gap-1">
                            🔄 Reactivate
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {listings.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">
                  No listings found
                </div>
                <p className="text-gray-500 mb-4">
                  Create your first listing to get started
                </p>
                <Link
                  to="/user/listings/add"
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f]"
                >
                  Add New Listing
                </Link>
              </div>
            )}
          </>
        )}

        {/* Pagination */}
        <div className="flex justify-between items-center mt-8">
          <span className="text-sm text-gray-400">
            Showing 6 of 24 listings
          </span>
          <div className="flex gap-1">
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 text-sm border rounded bg-[#E63946] text-white border-[#E63946]">
              1
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              2
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              3
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              4
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              5
            </button>
            <button className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
              &gt;
            </button>
          </div>
        </div>

        {/* Currency Converter Widget */}
        <div className="fixed bottom-4 left-4 bg-white rounded-lg shadow-lg p-4 w-64 border border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">
            Currency Converter
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="50"
                defaultValue="50"
                className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">eBa$</span>
            </div>
            <div className="flex items-center space-x-2">
              <select className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">
                <option>USD</option>
                <option>CAD</option>
                <option>JMD</option>
              </select>
              <span className="text-sm text-gray-600">= 72.50 USD</span>
            </div>
            <p className="text-xs text-gray-500">eBa$50 = 72.50 USD</p>
          </div>
        </div>

        {/* Inbox Widget */}
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-xs">📧</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Inbox 3</p>
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xs text-gray-600">Alex Johnson</span>
                <button className="text-xs text-gray-400">⌄</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserListingsListPage;
