import { lazy } from "react";

export const AddAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AddAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ViewAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/View/ViewAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const ListAdminWireframeTablePage = lazy(() => {
  const __import = import("@/pages/Admin/List/ListAdminWireframeTablePage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminLoginPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const AdminProfilePage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminSignUpPage = lazy(() => {
  const __import = import("@/pages/Admin/Auth/AdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const UserLoginPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserLoginPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const AdminDashboardPage = lazy(() => {
  const __import = import("@/pages/Admin/Dashboard/AdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const UserLandingPage = lazy(() => {
  const __import = import("@/pages/User/LandingPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("@/pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const UserDashboardPage = lazy(() => {
  const __import = import("@/pages/User/Dashboard/Dashboard.tsx");
  __import.finally(() => {});
  return __import;
});

// User Marketplace Pages
export const UserMarketplaceListPage = lazy(() => {
  const __import = import("@/pages/User/List/UserMarketplaceListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserMarketplaceDetailPage = lazy(() => {
  const __import = import("@/pages/User/View/UserMarketplaceDetailPage.tsx");
  __import.finally(() => {});
  return __import;
});

// User Listings Pages
export const UserListingsListPage = lazy(() => {
  const __import = import("@/pages/User/List/UserListingsListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserSoldActionNeededPage = lazy(() => {
  const __import = import("@/pages/User/List/UserSoldActionNeededPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserAddListingPage = lazy(() => {
  const __import = import("@/pages/User/Add/UserAddListingPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserEditListingPage = lazy(() => {
  const __import = import("@/pages/User/Edit/UserEditListingPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserViewListingPage = lazy(() => {
  const __import = import("@/pages/User/View/UserViewListingPage.tsx");
  __import.finally(() => {});
  return __import;
});

// User Transactions Pages
export const UserTransactionsListPage = lazy(() => {
  const __import = import("@/pages/User/List/UserTransactionsListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserViewTransactionPage = lazy(() => {
  const __import = import("@/pages/User/View/UserTransactionDetailsPage.tsx");
  __import.finally(() => {});
  return __import;
});

// User Rewards Pages
export const UserRewardsListPage = lazy(() => {
  const __import = import("@/pages/User/List/UserRewardsListPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserAvailableDeliveriesPage = lazy(() => {
  const __import = import("@/pages/User/List/UserAvailableDeliveriesPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserMyDeliveriesPage = lazy(() => {
  const __import = import("@/pages/User/List/UserMyDeliveriesPage.tsx");
  __import.finally(() => {});
  return __import;
});

// User Account Pages
export const UserAccountPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserAccountPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserDeliverySettingsPage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserDeliverySettingsPage.tsx");
  __import.finally(() => {});
  return __import;
});

export const UserProfilePage = lazy(() => {
  const __import = import("@/pages/User/Auth/UserProfilePage.tsx");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminUsersListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminUsersListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminCategoriesListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminCategoriesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCategoryPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCategoryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCategoryPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCategoryPage");
  __import.finally(() => {});
  return __import;
});

export const AdminListingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTopUpRequestsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTopUpRequestsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminTransactionsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminTransactionsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDisputesAndRefundsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminDisputesAndRefundsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryApplicationsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminDeliveryApplicationsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminRewardsAndReferralsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminRewardsAndReferralsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminViewTransactionPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewTransactionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminPromotionsAndSponsorshipsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminPromotionsAndSponsorshipsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminPlatformSettingsListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminPlatformSettingsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminReportedListingListPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminReportedListingListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewListingPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewListingPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryAgentComplaintsListPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/List/AdminDeliveryAgentComplaintsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminDeliveryAgentComplaintDetailsPage = lazy(() => {
  const __import = import(
    "@/pages/Admin/View/AdminDeliveryAgentComplaintDetailsPage"
  );
  __import.finally(() => {});
  return __import;
});

// OTHERS

export const TestComponents = lazy(() => {
  const __import = import("@/pages/PG/Custom/TestComponents");
  __import.finally(() => {});
  return __import;
});
