import React, { useEffect, useState, useCallback } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { PaginationBar } from "@/components/PaginationBar";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface IReward {
  id: number;
  type: string; // 'referral', 'commission', 'bonus'
  amount: string;
  status: string; // 'pending', 'paid', 'cancelled'
  description: string;
  earned_date: string;
  paid_date?: string;
  referral_name?: string;
  transaction_id?: number;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

interface IRewardsSummary {
  total_earned: string;
  total_pending: string;
  total_paid: string;
  active_referrals: number;
  this_month_earnings: string;
}

const UserRewardsListPage = () => {
  const [rewards, setRewards] = useState<IReward[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [summary, setSummary] = useState<IRewardsSummary>({
    total_earned: "0.00",
    total_pending: "0.00",
    total_paid: "0.00",
    active_referrals: 0,
    this_month_earnings: "0.00",
  });
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    type: "",
    status: "",
  });
  const [searchInput, setSearchInput] = useState("");

  const { sdk } = useSDK();

  const fetchRewards = async () => {
    setLoading(true);
    try {
      console.log("Fetching user rewards with filters:", filters);

      const params = {
        page: filters.page,
        limit: filters.limit,
        ...(filters.search && { search: filters.search }),
        ...(filters.type && { type: filters.type }),
        ...(filters.status && { status: filters.status }),
        // Add user_id filter to only show current user's rewards
        // user_id: currentUserId, // This would come from auth context
      };

      const result = await sdk.callRestAPI(
        {
          where: params,
        },
        "PAGINATE"
      );

      if (result.error) {
        console.error("Error fetching rewards:", result.message);
        setRewards([]);
        setPagination(null);
      } else {
        setRewards(result.list || []);
        setPagination(result.pagination || null);
      }
    } catch (error) {
      console.error("Error fetching rewards:", error);
      setRewards([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchSummary = async () => {
    try {
      const result = await sdk.callRestAPI({}, "GET");

      if (!result.error && result.data) {
        setSummary(result.data);
      }
    } catch (error) {
      console.error("Error fetching rewards summary:", error);
    }
  };

  useEffect(() => {
    fetchRewards();
    fetchSummary();
  }, [filters.page, filters.type, filters.status]);

  const handleSearch = useCallback(() => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
      page: 1,
    }));
  }, [searchInput]);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleTypeFilter = (type: string) => {
    setFilters((prev) => ({
      ...prev,
      type: type === "all" ? "" : type,
      page: 1,
    }));
  };

  const handleStatusFilter = (status: string) => {
    setFilters((prev) => ({
      ...prev,
      status: status === "all" ? "" : status,
      page: 1,
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "paid":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "referral":
        return "bg-blue-100 text-blue-800";
      case "commission":
        return "bg-purple-100 text-purple-800";
      case "bonus":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const typeTabs = [
    { key: "all", label: "All Types" },
    { key: "referral", label: "Referrals" },
    { key: "commission", label: "Commissions" },
    { key: "bonus", label: "Bonuses" },
  ];

  const statusTabs = [
    { key: "all", label: "All Status" },
    { key: "pending", label: "Pending" },
    { key: "paid", label: "Paid" },
    { key: "cancelled", label: "Cancelled" },
  ];

  return (
    <UserWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">My Rewards</h1>
          <p className="text-gray-300">
            Track your earnings from referrals and commissions
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">
              Total Earned
            </div>
            <div className="text-2xl font-bold text-[#e53e3e]">
              eBa$ {summary.total_earned}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Pending</div>
            <div className="text-2xl font-bold text-yellow-600">
              eBa$ {summary.total_pending}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">Paid Out</div>
            <div className="text-2xl font-bold text-green-600">
              eBa$ {summary.total_paid}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">
              Active Referrals
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {summary.active_referrals}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-sm font-medium text-gray-500">This Month</div>
            <div className="text-2xl font-bold text-purple-600">
              eBa$ {summary.this_month_earnings}
            </div>
          </div>
        </div>

        {/* Type Tabs */}
        <div className="bg-white rounded-lg p-4 mb-4">
          <div className="flex space-x-1">
            {typeTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleTypeFilter(tab.key)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  filters.type === tab.key ||
                  (filters.type === "" && tab.key === "all")
                    ? "bg-[#e53e3e] text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Status Tabs */}
        <div className="bg-white rounded-lg p-4 mb-4">
          <div className="flex space-x-1">
            {statusTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleStatusFilter(tab.key)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  filters.status === tab.key ||
                  (filters.status === "" && tab.key === "all")
                    ? "bg-[#e53e3e] text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg p-4 mb-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <MkdInputV2
                placeholder="Search rewards..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              >
                <MkdInputV2.Field
                  onKeyPress={(e: any) => e.key === "Enter" && handleSearch()}
                />
              </MkdInputV2>
            </div>
            <InteractiveButton
              onClick={handleSearch}
              className="bg-[#e53e3e] text-white px-6 py-2 rounded-md hover:bg-[#c53030]"
            >
              Search
            </InteractiveButton>
          </div>
        </div>

        {/* Rewards Table */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <MkdLoader />
          </div>
        ) : (
          <>
            <div className="bg-white rounded-lg overflow-hidden mb-6">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Earned Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Paid Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {rewards.map((reward) => (
                      <tr key={reward.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900">
                            {reward.description}
                          </div>
                          {reward.referral_name && (
                            <div className="text-sm text-gray-500">
                              Referral: {reward.referral_name}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(reward.type)}`}
                          >
                            {reward.type.charAt(0).toUpperCase() +
                              reward.type.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            eBa$ {reward.amount}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(reward.status)}`}
                          >
                            {reward.status.charAt(0).toUpperCase() +
                              reward.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(reward.earned_date).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {reward.paid_date
                            ? new Date(reward.paid_date).toLocaleDateString()
                            : "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {rewards.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-2">
                    No rewards found
                  </div>
                  <p className="text-gray-500 mb-4">
                    Start referring friends to earn rewards!
                  </p>
                  <InteractiveButton
                    onClick={() =>
                      alert("Referral functionality would be implemented here")
                    }
                    className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
                  >
                    Invite Friends
                  </InteractiveButton>
                </div>
              )}
            </div>

            {pagination && pagination.num_pages > 1 && (
              <div className="flex justify-center">
                <PaginationBar
                  currentPage={pagination.page}
                  pageCount={pagination.num_pages}
                  updateCurrentPage={handlePageChange}
                  pageSize={filters.limit}
                  canPreviousPage={pagination.has_prev}
                  canNextPage={pagination.has_next}
                  updatePageSize={() => {}}
                  startSize={1}
                  multiplier={1}
                  canChangeLimit={false}
                />
              </div>
            )}
          </>
        )}

        {/* Referral Section */}
        <div className="bg-white rounded-lg p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">
            Invite Friends & Earn Rewards
          </h2>
          <p className="text-gray-600 mb-4">
            Share eBaDollar with your friends and earn commissions on their
            transactions!
          </p>
          <div className="flex gap-4">
            <InteractiveButton
              onClick={() =>
                alert(
                  "Generate referral link functionality would be implemented here"
                )
              }
              className="bg-[#e53e3e] text-white px-6 py-2 rounded-md hover:bg-[#c53030]"
            >
              Get Referral Link
            </InteractiveButton>
            <InteractiveButton
              onClick={() =>
                alert("Share functionality would be implemented here")
              }
              className="border border-[#e53e3e] text-[#e53e3e] px-6 py-2 rounded-md hover:bg-[#e53e3e] hover:text-white"
            >
              Share on Social Media
            </InteractiveButton>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserRewardsListPage;
