import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import UserSoldActionNeededPage from '../../pages/User/List/UserSoldActionNeededPage';

// Mock the UserWrapper component
jest.mock('../../components/UserWrapper', () => {
  return {
    UserWrapper: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="user-wrapper">{children}</div>
    ),
  };
});

// Mock the MkdLoader component
jest.mock('../../components/MkdLoader', () => {
  return {
    MkdLoader: () => <div data-testid="loader">Loading...</div>,
  };
});

// Mock the useSDK hook
jest.mock('../../hooks/useSDK', () => ({
  useSDK: () => ({
    sdk: {
      request: jest.fn(),
    },
  }),
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  Link: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('UserSoldActionNeededPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the page title correctly', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('My Listings')).toBeInTheDocument();
  });

  test('renders the tabs correctly', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('All Listings')).toBeInTheDocument();
    expect(screen.getByText('Sold & Action Needed')).toBeInTheDocument();
  });

  test('renders the search input', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByPlaceholderText('Search by item name, category, or tag')).toBeInTheDocument();
  });

  test('renders filter buttons', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Sort')).toBeInTheDocument();
  });

  test('navigates to all listings when All Listings tab is clicked', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    const allListingsTab = screen.getByText('All Listings');
    fireEvent.click(allListingsTab);
    expect(mockNavigate).toHaveBeenCalledWith('/user/listings');
  });

  test('renders Add New Listing button', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('+ Add New Listing')).toBeInTheDocument();
  });

  test('shows loading state initially', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  test('renders currency converter widget', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('Currency Converter')).toBeInTheDocument();
  });

  test('renders inbox widget', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('Inbox 3')).toBeInTheDocument();
    expect(screen.getByText('Alex Johnson')).toBeInTheDocument();
  });

  test('renders pagination', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    expect(screen.getByText('Showing 6 of 24 listings')).toBeInTheDocument();
  });

  test('search input updates on change', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    const searchInput = screen.getByPlaceholderText('Search by item name, category, or tag') as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    expect(searchInput.value).toBe('test search');
  });

  test('filters toggle when filter button is clicked', () => {
    renderWithProviders(<UserSoldActionNeededPage />);
    const filtersButton = screen.getByText('Filters');
    
    // Initially filters should be visible
    expect(screen.getByText('Status')).toBeInTheDocument();
    
    // Click to hide filters
    fireEvent.click(filtersButton);
    
    // Click again to show filters
    fireEvent.click(filtersButton);
    expect(screen.getByText('Status')).toBeInTheDocument();
  });
});
