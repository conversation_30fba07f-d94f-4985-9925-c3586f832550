import { NavLink, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useProfile } from "@/hooks/useProfile";

const UserHeader = () => {
  const { profile } = useProfile();
  const navigate = useNavigate();
  const [amount, setAmount] = useState("0");
  const [fromCurrency, setFromCurrency] = useState("USD");
  const [toCurrency, setToCurrency] = useState("eBa$");
  const [deliveryDropdownOpen, setDeliveryDropdownOpen] = useState(false);

  // Mock conversion rate - in real app, this would come from an API
  const conversionRate = 72.5;
  const convertedAmount =
    fromCurrency === "USD" && toCurrency === "eBa$"
      ? (parseFloat(amount) * conversionRate).toFixed(2)
      : parseFloat(amount).toFixed(2);

  // Check if user is approved as delivery agent
  // This would typically come from the user profile or a separate API call
  const isDeliveryAgent = profile?.delivery_agent_status === "approved" || true; // Mock: set to true for demo

  const menuItems = [
    { name: "Dashboard", path: "/user/dashboard" },
    { name: "Marketplace", path: "/user/marketplace" },
    { name: "My Listings", path: "/user/listings" },
    { name: "My Transactions", path: "/user/transactions" },
    { name: "My Rewards", path: "/user/rewards" },
    { name: "My Account", path: "/user/account" },
  ];

  const deliveryMenuItems = [
    { name: "My Deliveries", path: "/user/deliveries", icon: "📦" },
    {
      name: "Available Deliveries",
      path: "/user/available-deliveries",
      icon: "📋",
    },
    { name: "Delivery Settings", path: "/user/delivery-settings", icon: "⚙️" },
  ];

  return (
    <div className="flex h-screen w-[250px] flex-col bg-[#0F2C59] text-white border-r border-gray-600">
      <div className="p-4 border-b border-gray-600">
        {/* eBaDollar Logo */}
        <div className="flex items-center">
          <h1 className="text-2xl font-bold">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>
      </div>
      <nav className="flex flex-col p-4">
        {menuItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `mb-2 rounded-md p-2 ${
                isActive ? "bg-[#E63946]" : "hover:bg-gray-700"
              }`
            }
          >
            {item.name}
          </NavLink>
        ))}

        {/* Delivery Agent Section */}
        {isDeliveryAgent && (
          <div className="mt-4 pt-4 border-t border-gray-600">
            {/* Delivery Dropdown Header */}
            <button
              onClick={() => setDeliveryDropdownOpen(!deliveryDropdownOpen)}
              className="mb-2 w-full flex items-center justify-between rounded-md p-2 text-left hover:bg-gray-700"
            >
              <div className="flex items-center">
                <span className="mr-2">🚚</span>
                <span>eBa Delivery</span>
              </div>
              <span
                className={`transform transition-transform ${deliveryDropdownOpen ? "rotate-180" : ""}`}
              >
                ▼
              </span>
            </button>

            {/* Delivery Menu Items */}
            {deliveryDropdownOpen && (
              <div className="ml-4 space-y-1">
                {deliveryMenuItems.map((item) => (
                  <NavLink
                    key={item.name}
                    to={item.path}
                    className={({ isActive }) =>
                      `flex items-center rounded-md p-2 text-sm ${
                        isActive ? "bg-[#E63946]" : "hover:bg-gray-700"
                      }`
                    }
                  >
                    <span className="mr-2">{item.icon}</span>
                    {item.name}
                  </NavLink>
                ))}
              </div>
            )}
          </div>
        )}
      </nav>
      <div className="mt-auto p-4 border-t border-gray-600">
        {/* Currency Converter */}
        <div className="mb-4 rounded-lg bg-[#0F2C59] p-3 border border-gray-600">
          <h3 className="mb-3 text-sm font-medium text-white">
            Currency Converter
          </h3>

          {/* Amount Input */}
          <div className="mb-3">
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="w-full rounded border border-gray-600 bg-gray-700 px-2 py-1 text-sm text-white placeholder-gray-400 focus:border-blue-400 focus:outline-none"
              placeholder="0"
            />
          </div>

          {/* From Currency Dropdown */}
          <div className="mb-3">
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="w-full rounded border border-gray-600 bg-gray-700 px-2 py-1 text-sm text-white focus:border-blue-400 focus:outline-none"
            >
              <option value="USD">USD</option>
              <option value="eBa$">eBa$</option>
            </select>
          </div>

          {/* Conversion Result */}
          <div className="flex items-center justify-between rounded bg-gray-800 px-2 py-1 text-sm">
            <span className="text-white">
              {toCurrency}${convertedAmount}
            </span>
            <button
              onClick={() => {
                setFromCurrency(toCurrency);
                setToCurrency(fromCurrency);
              }}
              className="text-[#E63946] hover:text-red-400"
            >
              ⇄
            </button>
          </div>
        </div>

        {/* Inbox Section */}
        <div className="mb-4 pb-4 border-b border-gray-600">
          <div className="flex items-center text-sm text-white">
            <span className="mr-2">📧</span>
            <span>Inbox</span>
            <span className="ml-auto rounded-full bg-[#E63946] px-2 py-1 text-xs">
              3
            </span>
          </div>
        </div>

        {/* Profile Section */}
        <div
          className="flex items-center pt-2 cursor-pointer hover:bg-gray-700 rounded-md p-2 -m-2"
          onClick={() => navigate("/user/profile")}
        >
          <div className="mr-3 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center">
            <span className="text-sm text-white">👤</span>
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-white">Alex Johnson</div>
          </div>
          <button className="text-gray-400 hover:text-white">⌄</button>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
